/**
 * 文本生成器模块 - 简化版本 (V6.0)
 *
 * 职责：基于策略简报生成高质量文案（流式模式）
 * 架构：基于内联模板的简洁实现
 *
 * 设计原则：
 * - 约定大于配置：使用内联模板，最小化配置
 * - 保持代码精简：删除复杂的推理解析逻辑
 * - 类型安全优先：严格的TypeScript类型定义
 * - 尽早报错退出：明确的参数校验和错误处理
 * - 单一职责原则：专注于文案生成
 */

import { BaseModule } from "./BaseModule";
import { createStreamlineLogger } from "../utils/StreamlineLogger";
import {
  createSelfDescribingData,
  callProgressCallback,
} from "../utils/AdaptiveMetadataDisplayManager";
import type { UserData } from "@/types/user-data";
import type {
  AnalyzerOutput,
  StrategistOutput,
  WriterOutput,
  UserPreferences,
} from "../../types";

interface WritingContext {
  strategy: StrategistOutput;
  data: UserData;
  analysis: AnalyzerOutput;
  preferences: UserPreferences;
}

interface GenerationResult {
  generatedText: string;
  confidence: number;
  templateUsed: string;
}

interface ContextStageResult {
  strategyId: string;
  targetEmotion: string;
  templatePath: string;
  context: WritingContext;
}

interface TextGenerationStageResult {
  generatedText: string;
  textLength: number;
  confidence: number;
  templateUsed: string;
  generationResult: GenerationResult;
}

export class WriterModule extends BaseModule {
  private static readonly TEMPLATE_NAME = "strategist-to-text-inline";

  constructor() {
    super("writer", "writer", {
      enableLogging: true,
      enableMetrics: true,
      timeout: 60000,
    });
  }

  /**
   * 流式文本生成方法 - 基于内联模板的简化实现
   */
  async generateTextWithStreaming(
    githubData: UserData,
    analyzerOutput: AnalyzerOutput,
    strategistOutput: StrategistOutput,
    userPreferences: UserPreferences,
    onProgress: (
      message: string,
      chunk: string,
      isComplete: boolean,
      stage: string
    ) => void
  ): Promise<WriterOutput> {
    const startTime = Date.now();

    return this.executeWithWrapper(
      async () => {
        // 创建StreamlineLogger实例
        const logger = createStreamlineLogger(
          this.config.debugSessionId || `writer-session-${Date.now()}`,
          "writer"
        );

        try {
          // Stage 1: 输入验证 - 尽早报错退出
          this.validateInput(strategistOutput);

          // 🚀 自描述数据展示验证结果
          const validateResult = createSelfDescribingData(
            {
              status: "ok",
              hasStrategy: !!strategistOutput.content,
              templateName: WriterModule.TEMPLATE_NAME,
            },
            {
              title: "🔍 输入验证完成",
              color: "#10b981",
            }
          );

          callProgressCallback(
            onProgress,
            "输入验证完成",
            validateResult,
            true,
            "input_validation"
          );

          // Stage 2: 构建写作上下文
          const contextResult = await logger.executeStage<ContextStageResult>(
            "构建写作上下文",
            async (
              reportProgress: (progress: number, message: string) => void
            ) => {
              reportProgress(20, "正在构建写作上下文...");

              const context = this.buildWritingContext(
                githubData,
                analyzerOutput,
                strategistOutput,
                userPreferences
              );

              reportProgress(100, "写作上下文构建完成");

              return {
                strategyId: context.strategy.content.insight_pool.persona,
                targetEmotion:
                  context.strategy.content.insight_pool.humor_angle,
                templatePath: WriterModule.TEMPLATE_NAME,
                context,
              };
            }
          );
          const context = contextResult.context;

          // Stage 3: 流式模板化文案生成
          const textGenerationResult =
            await logger.executeStage<TextGenerationStageResult>(
              "流式模板化文案生成",
              async (
                reportProgress: (progress: number, message: string) => void
              ) => {
                reportProgress(30, "正在生成文案...");

                const generationResult = await this.streamingTemplateGeneration(
                  context,
                  (message, chunk, isComplete, stage) => {
                    // 转发流式内容到外部回调
                    onProgress(message, chunk, isComplete, stage);

                    // 同时报告内部进度
                    if (chunk) {
                      reportProgress(80, "AI文案生成中...");
                    }
                  }
                );

                reportProgress(
                  100,
                  `生成了${generationResult.generatedText.length}字的文案`
                );

                return {
                  generatedText: generationResult.generatedText,
                  textLength: generationResult.generatedText.length,
                  confidence: generationResult.confidence,
                  templateUsed: generationResult.templateUsed,
                  generationResult,
                };
              }
            );
          const generationResult = textGenerationResult.generationResult;
          onProgress("文案生成完成", "", true, "text_generation_complete");

          const processingTime = Date.now() - startTime;

          // 构造简化的WriterOutput
          const writerOutput: WriterOutput = {
            status: "success",
            content: {
              generatedText: generationResult.generatedText,
              confidence: generationResult.confidence,
              usedTemplate: generationResult.templateUsed,
              iterations: 1,
              qualityScore: generationResult.confidence,
            },
            processing_time: processingTime,
            metadata: {
              writerModuleVersion: "V6.0-Inline",
              templateBased: true,
              templatePath: WriterModule.TEMPLATE_NAME,
            },
          };

          return writerOutput;
        } catch (error) {
          // 🚀 应用自描述协议优化错误信息展示
          const errorInfo = createSelfDescribingData(
            {
              errorType: "text_generation_error",
              message: error instanceof Error ? error.message : "未知错误",
              stage: "writer_execution",
              timestamp: new Date().toISOString(),
            },
            {
              title: "❌ 文案生成失败",
              color: "#ef4444",
            }
          );

          callProgressCallback(
            onProgress,
            "文案生成过程中发生错误",
            errorInfo,
            true,
            "text_generation_error"
          );

          throw error;
        }
      },
      { githubData, analyzerOutput, strategistOutput, userPreferences }
    );
  }

  /**
   * 流式模板化文案生成 - 简化实现
   */
  private async streamingTemplateGeneration(
    context: WritingContext,
    onProgress: (
      message: string,
      chunk: string,
      isComplete: boolean,
      stage: string
    ) => void
  ): Promise<GenerationResult> {
    try {
      // 直接构建模板prompt
      const templatePrompt = this.buildTemplatePrompt(context.strategy);
      let accumulatedText = "";

      onProgress(
        "WriterModule Prompt",
        templatePrompt,
        true,
        "text_generation_prompt"
      );

      // V6.1: 使用约定大于配置的简化调用方式
      const streamGenerator = this.callLLMStream({
        messages: [
          {
            role: "user",
            content: templatePrompt,
          },
        ],
        temperature: 0.8, // 适中的创意程度
        max_tokens: 800,
        stream: true,
      });

      // 🚀 简化的流式处理
      let chunkCount = 0;
      for await (const chunk of streamGenerator) {
        chunkCount++;
        if (chunkCount === 1) {
          // 🚀 立即显示AI正在思考的状态
          onProgress(
            "🤔 AI正在创作文案...",
            "🔄",
            false,
            "text_generation_content"
          );
        }

        const content = chunk.choices[0]?.delta?.content;
        if (content) {
          accumulatedText += content;

          // 🚀 第一次收到真正内容时，更新状态消息
          const statusMessage =
            accumulatedText.length === content.length
              ? "💡 AI开始生成文案..."
              : "AI文案创作中...";

          onProgress(statusMessage, content, false, "text_generation_content");
        }
      }

      // 发送完成事件
      onProgress("✅ AI文案创作完成", "💯", true, "text_generation_content");

      // 简化处理：直接使用生成的文本，无需复杂解析
      const cleanedText = this.cleanGeneratedText(accumulatedText);

      return {
        generatedText: cleanedText,
        confidence: 0.85, // 基于模板的高置信度
        templateUsed: WriterModule.TEMPLATE_NAME,
      };
    } catch (error) {
      console.error("WriterModule template generation failed:", error);
      throw error;
    }
  }

  /**
   * 输入验证 - 尽早报错退出原则
   */
  private validateInput(strategistOutput: StrategistOutput): void {
    if (!strategistOutput) {
      throw new Error("策略输出不能为空");
    }

    if (!strategistOutput.content) {
      throw new Error("策略输出内容不能为空");
    }

    // 简化验证：检查必要字段
    const { content } = strategistOutput;
    if (!content.narrative_scaffold) {
      throw new Error("缺少必需的narrative_scaffold数据");
    }
    if (!content.insight_pool) {
      throw new Error("缺少必需的insight_pool数据");
    }
    if (!content.punchline_focus) {
      throw new Error("缺少必需的punchline_focus数据");
    }
  }

  /**
   * 构建写作上下文 - 简化版本
   */
  private buildWritingContext(
    githubData: UserData,
    analyzerOutput: AnalyzerOutput,
    strategistOutput: StrategistOutput,
    userPreferences: UserPreferences
  ): WritingContext {
    return {
      strategy: strategistOutput,
      data: githubData,
      analysis: analyzerOutput,
      preferences: userPreferences,
    };
  }

  /**
   * 基于内联模板构建Prompt - 参考其他模块的实现
   */
  private buildTemplatePrompt(strategistOutput: StrategistOutput): string {
    const { content } = strategistOutput;

    return `你是一位专为社交平台写短文案的中文脱口秀编剧，风格接近李诞，擅长用轻松、克制、带点哲理的语气，写出令人会心一笑又略感共鸣的短句。
现在，请根据以下用户画像数据，生成一条【适合用户自己在社交平台上发布】的个性化短文案。要求如下：

【输出目标】

1. 最终文案限 1 ～ 3 句话，总长度不超过 100 字；
2. 语气要轻盈、聪明、有钩子，能引发旁人模仿转发；
3. 文案应“夸得巧妙”，让本人乐于转发，别人看了也会想知道“如果是我，会生成什么”；
4. 可以使用类比、反差、自嘲等技巧，但禁止浮夸堆砌；
5. 请勿输出解释或格式化提示，只输出文案本身；
6. 允许轻微荒诞，但必须有哲理式的落点（可参考 punchline）。

【用户行为画像数据】

- 📌 编码行为描述：
  ${content.narrative_scaffold.A}
  ${content.narrative_scaffold.B}

- 🎭 个性设定（persona）：
  ${content.insight_pool.persona}

- 🤹 幽默角度（humor angle）：
  ${content.insight_pool.humor_angle}

- 🎯 类比池：

  - ${content.insight_pool.analogy_pool[0]}
  - ${content.insight_pool.analogy_pool[1]}
  - ${content.insight_pool.analogy_pool[2]}

- 💡 推荐 punchline（可选采纳）：

  - ${content.punchline_focus.punchline_candidates[0]}
  - ${content.punchline_focus.punchline_candidates[1]}
  - ${content.punchline_focus.punchline_candidates[2]}

- ✍️ 写作建议：
  - tone: ${content.punchline_focus.tone}
  - style: ${content.punchline_focus.stylistic_suggestion}
  - instruction: ${content.writing_instruction.focus_on}

请基于上述信息，生成一条吸引人的短文案，用于社交平台发布，模仿李诞脱口秀风格。
`;
  }

  /**
   * 清理生成的文本 - 保留基本清理逻辑
   */
  private cleanGeneratedText(text: string): string {
    return text
      .replace(/^["']|["']$/g, "") // 移除首尾引号
      .replace(/\n+/g, " ") // 合并换行
      .replace(/\s+/g, " ") // 合并空格
      .trim();
  }

  /**
   * 实现抽象方法 - 健康检查
   */
  protected async performHealthCheck(): Promise<boolean> {
    try {
      // 检查基本组件
      if (!this.llmClient) {
        return false;
      }

      // 测试模板构建功能
      const mockStrategistOutput: StrategistOutput = {
        status: "success",
        content: {
          narrative_scaffold: {
            A: "测试人物出场",
            B: "测试行为反差",
            C_hint: "测试结尾提示",
          },
          insight_pool: {
            behavior_summary: "测试行为总结",
            persona: "测试人设",
            humor_angle: "测试幽默角度",
            analogy_pool: ["测试类比1", "测试类比2", "测试类比3"],
          },
          punchline_focus: {
            tone: "测试语气",
            punchline_candidates: ["候选1", "候选2", "候选3", "候选4"],
            stylistic_suggestion: "测试文风建议",
          },
          writing_instruction: {
            focus_on: "测试重点",
            overall_tone: "测试整体语气",
          },
        },
      };

      try {
        const prompt = this.buildTemplatePrompt(mockStrategistOutput);
        return prompt.length > 0;
      } catch (error) {
        console.error("模板构建测试失败:", error);
        return false;
      }
    } catch {
      return false;
    }
  }
}
