/**
 * OpenRouter API Client - 统一接口实现
 *
 * 基于OpenRouter官方API文档: https://openrouter.ai/docs/api-reference/overview
 *
 * ✨ 核心特性:
 * - 🌐 多模型聚合：支持OpenAI、Anthropic、DeepSeek等主流模型
 * - 🎯 智能路由：自动选择最优性价比模型
 * - 💰 成本优化：实时价格比较和路由选择
 * - 🔄 容错机制：自动故障转移和重试
 * - 📊 结构化输出：JSON模式原生支持
 * - 🌊 SSE流式处理：实时响应流
 *
 * 🏗️ 设计原则:
 * - 接口完全兼容DoubaoClient，确保无缝切换
 * - 约定大于配置：提供合理默认值，最小化配置需求
 * - 尽早报错退出：构造函数验证环境变量
 * - 类型安全优先：严格TypeScript类型定义
 *
 * 📖 使用示例:
 *
 * // 基础文本对话
 * const client = new OpenRouterClient();
 * const response = await client.chat({
 *   messages: [{ role: "user", content: "Hello, world!" }],
 *   temperature: 0.7,
 *   max_tokens: 1000
 * });
 *
 * // JSON模式结构化输出
 * const jsonResponse = await client.chat({
 *   messages: [{ role: "user", content: "Generate user profile data" }],
 *   response_format: { type: "json_object" }
 * });
 *
 * // 流式响应
 * for await (const chunk of client.chatStream({
 *   messages: [{ role: "user", content: "Write a story" }],
 *   stream: true
 * })) {
 *   console.log(chunk.choices[0]?.delta?.content || "");
 * }
 *
 * 🔧 环境配置:
 * - OPENROUTER_API_KEY: OpenRouter API密钥 (必需)
 * - OPENROUTER_DEFAULT_MODEL: 默认模型 (可选，默认gpt-4o-mini)
 * - OPENROUTER_APP_NAME: 应用名称 (可选，默认GitHub-Card)
 * - OPENROUTER_ENABLE_ROUTING: 启用智能路由 (可选，默认false)
 */

import {
  ILLMClient,
  TokenUsageStats,
  LLMClientError,
  DEFAULT_LLM_CONFIG,
  SUPPORTED_MODELS,
} from "./ILLMClient";

import type {
  ChatOptions,
  ChatResponse,
  StreamChunk,
  RequestMetrics,
  ChatMessage,
  DoubaoResponseFormat,
} from "./DoubaoClient";

// =================================================================
// OpenRouter API 类型定义
// =================================================================

/**
 * OpenRouter API 请求格式
 * 基于官方文档: https://openrouter.ai/docs/api-reference/overview
 */
interface OpenRouterRequest {
  model: string;
  messages: ChatMessage[];
  temperature?: number;
  max_tokens?: number;
  stream?: boolean;

  // OpenRouter结构化输出
  response_format?: { type: "json_object" };

  // OpenRouter独有参数
  models?: string[]; // 模型路由
  route?: "fallback"; // 故障转移
  provider?: {
    // 提供商偏好
    order: string[];
    require_parameters?: boolean;
  };
  transforms?: string[]; // 提示词转换
  user?: string; // 用户标识

  // 标准LLM参数
  top_p?: number;
  top_k?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  stop?: string | string[];
  seed?: number;
}

/**
 * OpenRouter API 响应格式
 * 标准化为与DoubaoClient一致的格式
 */
interface OpenRouterResponse {
  id: string;
  choices: Array<{
    message: {
      content: string;
      role: string;
    };
    finish_reason: string;
    index: number;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  model: string;
  created: number;
  object: string;
}

/**
 * OpenRouter流式响应格式
 */
interface OpenRouterStreamChunk {
  id: string;
  choices: Array<{
    delta: {
      content?: string;
      role?: string;
    };
    finish_reason?: string;
    index: number;
  }>;
  model: string;
  created: number;
  object: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// =================================================================
// 错误处理类
// =================================================================

/**
 * OpenRouter特定错误类
 * 统一错误格式，便于调试和处理
 */
class OpenRouterError extends LLMClientError {
  readonly provider = "openrouter";
  readonly retryable: boolean;

  constructor(
    message: string,
    public readonly errorCode: string,
    public readonly statusCode?: number,
    originalError?: Error
  ) {
    super(message, originalError);

    // 根据错误码判断是否可重试
    this.retryable = this.isRetryableError(statusCode, errorCode);
  }

  private isRetryableError(statusCode?: number, errorCode?: string): boolean {
    // 可重试的错误类型
    const retryableStatusCodes = [408, 429, 500, 502, 503, 504];
    const retryableErrorCodes = [
      "rate_limit_exceeded",
      "server_error",
      "timeout",
      "connection_error",
    ];

    return (
      (statusCode !== undefined && retryableStatusCodes.includes(statusCode)) ||
      (errorCode !== undefined && retryableErrorCodes.includes(errorCode))
    );
  }
}

// =================================================================
// OpenRouterClient 主实现
// =================================================================

export class OpenRouterClient implements ILLMClient {
  private apiKey: string;
  private baseURL = "https://openrouter.ai/api/v1";
  private appName: string;
  private siteUrl: string;
  private defaultModel: string;
  private enableRouting: boolean;

  // 配置常量 - 与DoubaoClient保持一致
  private readonly DEFAULT_TIMEOUT = DEFAULT_LLM_CONFIG.TIMEOUT;
  private readonly DEFAULT_MAX_TOKENS = DEFAULT_LLM_CONFIG.MAX_TOKENS;
  private readonly DEFAULT_TEMPERATURE = DEFAULT_LLM_CONFIG.TEMPERATURE;
  private readonly MAX_RETRY_ATTEMPTS = DEFAULT_LLM_CONFIG.MAX_RETRIES;
  private readonly RETRY_DELAY_BASE = DEFAULT_LLM_CONFIG.RETRY_DELAY_BASE;

  // 性能追踪 - 与DoubaoClient完全一致
  private requestMetrics: Map<string, RequestMetrics> = new Map();
  private tokenUsageStats: TokenUsageStats = {
    totalRequests: 0,
    totalTokens: 0,
    avgTokensPerRequest: 0,
    lastUpdated: 0,
  };

  constructor() {
    // 尽早报错退出 - 验证必要的环境变量
    this.validateEnvironment();

    // 初始化配置 - 约定大于配置原则
    this.apiKey = process.env.OPENROUTER_API_KEY!;
    this.appName =
      process.env.OPENROUTER_APP_NAME ||
      DEFAULT_LLM_CONFIG.OPENROUTER_DEFAULT_APP_NAME;
    this.siteUrl =
      process.env.OPENROUTER_SITE_URL ||
      process.env.NEXTAUTH_URL ||
      "https://github-card.refined-x.workers.dev";
    this.defaultModel =
      process.env.OPENROUTER_DEFAULT_MODEL ||
      DEFAULT_LLM_CONFIG.OPENROUTER_DEFAULT_MODEL;
    this.enableRouting = process.env.OPENROUTER_ENABLE_ROUTING === "true";

    // 初始化性能统计
    this.tokenUsageStats = {
      totalRequests: 0,
      totalTokens: 0,
      avgTokensPerRequest: 0,
      lastUpdated: Date.now(),
    };

    console.log(
      `🌐 OpenRouterClient initialized with model: ${this.defaultModel}`
    );
    if (this.enableRouting) {
      console.log("🎯 Smart routing enabled for cost optimization");
    }
  }

  /**
   * 环境变量验证 - 尽早报错退出原则
   */
  private validateEnvironment(): void {
    if (!process.env.OPENROUTER_API_KEY) {
      throw new OpenRouterError(
        "OPENROUTER_API_KEY environment variable is required. " +
          "Please get your API key from https://openrouter.ai/keys and set it in your environment variables.",
        "missing_api_key"
      );
    }

    // 验证API key格式
    const apiKey = process.env.OPENROUTER_API_KEY;
    if (!apiKey.startsWith("sk-or-")) {
      console.warn(
        "OPENROUTER_API_KEY format appears invalid. Expected format: sk-or-v1-... " +
          "Please verify your API key from OpenRouter dashboard."
      );
    }
  }

  /**
   * 主聊天方法 - 与DoubaoClient.chat()完全兼容
   */
  async chat(options: ChatOptions): Promise<ChatResponse> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      // 初始化指标追踪
      this.requestMetrics.set(requestId, {
        requestId,
        startTime,
      });

      // 执行聊天请求
      return await this.executeChat(options, requestId);
    } catch (error) {
      this.handleRequestError(requestId, error);
      throw error;
    }
  }

  /**
   * 流式聊天方法 - 与DoubaoClient.chatStream()完全兼容
   */
  async *chatStream(
    options: ChatOptions
  ): AsyncGenerator<StreamChunk, void, unknown> {
    const requestId = this.generateRequestId();

    // 初始化指标追踪
    this.requestMetrics.set(requestId, {
      requestId,
      startTime: Date.now(),
    });

    const controller = new AbortController();
    const timeoutId = setTimeout(
      () => controller.abort(),
      options.timeout || this.DEFAULT_TIMEOUT
    );

    try {
      // 构建OpenRouter请求
      const openRouterRequest = this.transformChatOptions({
        ...options,
        stream: true,
      });

      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: "POST",
        headers: this.buildHeaders(requestId),
        body: JSON.stringify(openRouterRequest),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new OpenRouterError(
          `OpenRouter streaming API error: ${response.status} ${response.statusText}`,
          "api_error",
          response.status
        );
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new OpenRouterError(
          "Failed to get response stream reader",
          "stream_error"
        );
      }

      const decoder = new TextDecoder();
      let buffer = "";
      let totalTokens = 0;

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split("\n");
          buffer = lines.pop() || "";

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              const data = line.slice(6).trim();
              if (data === "[DONE]") {
                // 更新最终指标
                const metrics = this.requestMetrics.get(requestId);
                if (metrics) {
                  metrics.endTime = Date.now();
                  metrics.tokenUsage = {
                    prompt_tokens: 0,
                    completion_tokens: totalTokens,
                    total_tokens: totalTokens,
                  };
                }
                return;
              }

              try {
                const parsed: OpenRouterStreamChunk = JSON.parse(data);

                // 追踪token使用
                if (parsed.usage) {
                  totalTokens = parsed.usage.total_tokens || totalTokens;
                }

                // 转换为统一格式并yield
                yield this.transformStreamChunk(parsed);
              } catch (error) {
                console.warn("Failed to parse SSE chunk:", error);
                // 继续处理其他块
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
        clearTimeout(timeoutId);
      }
    } catch (error) {
      clearTimeout(timeoutId);
      this.handleRequestError(requestId, error);
      throw error;
    }
  }

  /**
   * 健康检查 - 与DoubaoClient.healthCheck()完全兼容
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.chat({
        messages: [{ role: "user", content: "ping" }],
        max_tokens: 10,
        timeout: 5000,
      });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取性能统计 - 与DoubaoClient.getPerformanceStats()完全兼容
   */
  getPerformanceStats(): TokenUsageStats {
    return { ...this.tokenUsageStats };
  }

  /**
   * 获取请求指标 - 与DoubaoClient.getRequestMetrics()完全兼容
   */
  getRequestMetrics(
    requestId?: string
  ): RequestMetrics | RequestMetrics[] | undefined {
    if (requestId) {
      return this.requestMetrics.get(requestId);
    }
    return Array.from(this.requestMetrics.values());
  }

  // =================================================================
  // 私有方法 - 核心实现逻辑
  // =================================================================

  /**
   * 执行聊天请求，支持重试机制
   */
  private async executeChat(
    options: ChatOptions,
    requestId: string,
    retryCount = 0
  ): Promise<ChatResponse> {
    const controller = new AbortController();
    const timeoutId = setTimeout(
      () => controller.abort(),
      options.timeout || this.DEFAULT_TIMEOUT
    );

    try {
      // 转换为OpenRouter请求格式
      const openRouterRequest = this.transformChatOptions(options);

      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: "POST",
        headers: this.buildHeaders(requestId),
        body: JSON.stringify(openRouterRequest),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new OpenRouterError(
          `OpenRouter API error: ${response.status} ${response.statusText}. ${errorText}`,
          "api_error",
          response.status
        );
      }

      const result: OpenRouterResponse = await response.json();

      // 更新指标
      this.updateRequestMetrics(requestId, result);

      // 转换为统一响应格式
      return this.transformResponse(result);
    } catch (error) {
      clearTimeout(timeoutId);

      // 重试逻辑
      if (retryCount < this.MAX_RETRY_ATTEMPTS && this.shouldRetry(error)) {
        await this.delay(this.RETRY_DELAY_BASE * Math.pow(2, retryCount));
        return this.executeChat(options, requestId, retryCount + 1);
      }

      if ((error as Error).name === "AbortError") {
        throw new OpenRouterError(
          `OpenRouter API timeout after ${
            options.timeout || this.DEFAULT_TIMEOUT
          }ms`,
          "timeout"
        );
      }

      throw error;
    }
  }

  /**
   * 转换聊天选项：DoubaoClient格式 -> OpenRouter格式
   */
  private transformChatOptions(options: ChatOptions): OpenRouterRequest {
    const request: OpenRouterRequest = {
      model: options.model || this.defaultModel,
      messages: options.messages,
      temperature: options.temperature || this.DEFAULT_TEMPERATURE,
      max_tokens: options.max_tokens || this.DEFAULT_MAX_TOKENS,
      stream: options.stream || false,
    };

    // 结构化输出支持
    if (options.response_format) {
      request.response_format = { type: "json_object" };
    }

    // OpenRouter智能路由功能
    if (this.enableRouting) {
      request.route = "fallback";
      request.models = [
        this.defaultModel,
        SUPPORTED_MODELS.OPENROUTER.GPT_4O_MINI,
        SUPPORTED_MODELS.OPENROUTER.CLAUDE_3_HAIKU,
      ];
    }

    // 应用标识 - 用于OpenRouter统计
    request.user = `${this.appName}-user`;

    return request;
  }

  /**
   * 转换响应格式：OpenRouter -> DoubaoClient统一格式
   */
  private transformResponse(
    openRouterResponse: OpenRouterResponse
  ): ChatResponse {
    return {
      choices: openRouterResponse.choices.map((choice) => ({
        message: {
          content: choice.message.content,
          role: choice.message.role,
        },
        finish_reason: choice.finish_reason,
      })),
      usage: {
        prompt_tokens: openRouterResponse.usage.prompt_tokens,
        completion_tokens: openRouterResponse.usage.completion_tokens,
        total_tokens: openRouterResponse.usage.total_tokens,
      },
      model: openRouterResponse.model,
      created: openRouterResponse.created,
    };
  }

  /**
   * 转换流式块：OpenRouter -> DoubaoClient统一格式
   */
  private transformStreamChunk(chunk: OpenRouterStreamChunk): StreamChunk {
    return {
      choices: chunk.choices.map((choice) => ({
        delta: {
          content: choice.delta.content,
          role: choice.delta.role,
        },
        finish_reason: choice.finish_reason,
      })),
      usage: chunk.usage
        ? {
            prompt_tokens: chunk.usage.prompt_tokens,
            completion_tokens: chunk.usage.completion_tokens,
            total_tokens: chunk.usage.total_tokens,
          }
        : undefined,
      model: chunk.model,
    };
  }

  /**
   * 构建请求头
   */
  private buildHeaders(requestId: string): Record<string, string> {
    return {
      Authorization: `Bearer ${this.apiKey}`,
      "Content-Type": "application/json",
      "X-Request-ID": requestId,
      // OpenRouter特定头部 - 用于应用识别和排名
      "HTTP-Referer": this.siteUrl,
      "X-Title": this.appName,
    };
  }

  /**
   * 判断是否应该重试
   */
  private shouldRetry(error: any): boolean {
    if (error instanceof OpenRouterError) {
      return error.retryable;
    }

    // 网络错误重试逻辑
    const retryableErrors = [
      "ECONNRESET",
      "ENOTFOUND",
      "ECONNREFUSED",
      "ETIMEDOUT",
    ];

    const errorMessage = error?.message?.toLowerCase() || "";
    return retryableErrors.some((err) =>
      errorMessage.includes(err.toLowerCase())
    );
  }

  /**
   * 延迟函数
   */
  private async delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * 更新请求指标
   */
  private updateRequestMetrics(
    requestId: string,
    response: OpenRouterResponse
  ): void {
    const metrics = this.requestMetrics.get(requestId);
    if (metrics) {
      metrics.endTime = Date.now();
      metrics.tokenUsage = response.usage;

      // 更新全局统计
      this.tokenUsageStats.totalRequests++;
      this.tokenUsageStats.totalTokens += response.usage.total_tokens;
      this.tokenUsageStats.avgTokensPerRequest =
        this.tokenUsageStats.totalTokens / this.tokenUsageStats.totalRequests;
      this.tokenUsageStats.lastUpdated = Date.now();
    }
  }

  /**
   * 处理请求错误
   */
  private handleRequestError(requestId: string, error: any): void {
    const metrics = this.requestMetrics.get(requestId);
    if (metrics) {
      metrics.endTime = Date.now();
    }

    console.error(`OpenRouter request ${requestId} failed:`, error);
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `openrouter_${Date.now()}_${Math.random()
      .toString(36)
      .substring(2, 11)}`;
  }
}
